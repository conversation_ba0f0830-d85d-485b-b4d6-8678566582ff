import {
  Alert,
  Avatar,
  Box,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { truncateText } from 'src/utils/truncate-text';

import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';

import { UserTeam } from 'src/services/api/use-my-teams-api';
import { useMyTemplatesView } from './use-my-templates-view';

// ----------------------------------------------------------------------

// Agent Card Component
const AgentCard = ({
  agent,
  id,
  onOpenMenu,
  handleId,
}: {
  agent: UserTeam;
  id: number;
  onOpenMenu?: (event: React.MouseEvent<HTMLElement>) => void;
  handleId?: (id: number) => void;
}) => {
  const { handleOpenAgent, isCreating } = useMyTemplatesView();

  return (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) =>
          `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: (theme) =>
            `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
          '&::before': {
            opacity: 1,
          },
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-start"
          mb={1}
          sx={{ minHeight: '75px' }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {agent.templateTeam.name}
            </Typography>
            <Chip
              label={agent.templateTeam.type}
              size="small"
              sx={{
                bgcolor: 'rgba(163, 139, 233, 0.33)',
                color: 'inherit',
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.025em',
                border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                borderRadius: '6px',
                height: '24px',
                '& .MuiChip-label': {
                  px: 1.5,
                },
                '&:hover': {
                  bgcolor: (theme) => theme.palette.primary.main + '10',
                  color: 'primary.main',
                  transform: 'scale(1.05)',
                },
              }}
            />
          </Box>
          <IconButton
            onClick={(e: React.MouseEvent<HTMLElement>) => {
              onOpenMenu?.(e);
              handleId?.(agent?.id);
            }}
            size="small"
            sx={{
              color: 'text.secondary',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: (theme) => theme.palette.primary.main + '10',
                color: 'primary.main',
                transform: 'scale(1.05)',
              },
            }}
          >
            <Iconify icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>

        {/* Model Information */}
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <Typography variant="body2" color="text.secondary" fontWeight={500}>
            Model:
          </Typography>
          <Chip
            label={agent.templateTeam.model.replace(/_/g, ' ')}
            size="small"
            sx={{
              bgcolor: 'main',
              fontSize: '0.7rem',
              height: '20px',
              '& .MuiChip-label': {
                px: 1,
              },
            }}
          />
        </Box>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ mb: 2, lineHeight: 1.4, minHeight: '100px' }}
        >
          {truncateText(agent?.templateTeam?.description, 30)}
        </Typography>

        {/* Special Request */}
        {/* {agent.specialRequest && (
          <Typography
            variant="body2"
            color="text.primary"
            sx={{ mb: 2, lineHeight: 1.4, fontStyle: 'italic' }}
          >
            Special Request: {agent.specialRequest}
          </Typography>
        )} */}

        {/* Category Information */}
        <Box display="flex" alignItems="center" gap={1} mb={3}>
          <Avatar
            sx={{
              width: 44,
              height: 44,
              bgcolor: agent.templateTeam.category.theme + '20',
              border: '1px solid',
              borderColor: agent.templateTeam.category.theme + '40',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                bgcolor: agent.templateTeam.category.theme + '30',
                borderColor: agent.templateTeam.category.theme,
              },
            }}
          >
            <Iconify
              icon={agent.templateTeam.category.icon}
              width={24}
              height={24}
              style={{ color: agent.templateTeam.category.theme }}
            />
          </Avatar>
          <Box>
            <Typography
              variant="body2"
              fontWeight={600}
              color="text.primary"
              sx={{ textTransform: 'capitalize' }}
            >
              {agent.templateTeam.category.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {agent.templateTeam.category.description}
            </Typography>
          </Box>
        </Box>

        <AppButton
          variant="outlined"
          isLoading={isCreating}
          color="primary"
          fullWidth
          onClick={() => handleOpenAgent(agent)}
          label="Use Agent"
        />
      </CardContent>
    </Card>
  );
};

export function MyTemplatesView() {
  // Use the agents view hook to manage state and data
  const {
    agents,
    filteredAgents,
    isInitialLoading,
    isLoadingMore,
    categoriesLoading,
    error,
    refetch,
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    handleSearch,
    handleOpenAgent,
    handleCategoryFilterChange,
    openMenu,
    handleClick,
    handleClose,
    anchorEl,
    hanldeDeleteAgent,
    openConfirmationDelete,
    setOpenConfirmationDelete,
    handleIdAgent,
    handleConfirmDelete,
    isPendingDeleteAgent,
    isLoading,
  } = useMyTemplatesView();

  // Show loading state (same as templates - wait for both agents and categories)
  if (isLoading || categoriesLoading) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1400px',
          mx: 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress size={40} />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1400px',
          mx: 'auto',
        }}
      >
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load agents. Please try again.
        </Alert>
        <AppButton variant="outlined" onClick={() => refetch()} label="Retry" />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        p: { xs: 2, sm: 3, md: 4 },
        maxWidth: '1400px',
        mx: 'auto',
      }}
    >
      {/* Header Section */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
        sx={{ position: 'relative', zIndex: 1 }}
      >
        <Box>
          <Typography variant="h3" fontWeight={800} color="text.primary">
            My Team’s Templates
          </Typography>
          <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)' }}>
            Enable advanced workflows with applications{' '}
          </Typography>
        </Box>
        <AppButton
          variant="contained"
          startIcon={<Iconify icon="eva:plus-fill" width={20} height={20} />}
          label="Create New Agent"
          fullWidth={false}
        />
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Search Bar */}
      <Box sx={{ mb: 3, width: '100%' }}>
        <TextField
          fullWidth
          placeholder="Search agents..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" width={20} sx={{ color: 'inherit' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            width: '100%',
            '& .MuiOutlinedInput-root': {
              borderColor: 'transparent',
              borderRadius: 1,
              bgcolor: 'rgba(24, 0, 72, 0.08)',
              height: 48,
              fontSize: '0.95rem',
              '&:hover': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                },
              },
              '&.Mui-focused': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                  borderWidth: 2,
                },
              },
            },
          }}
        />
      </Box>

      {/* Category Filter Tabs - Below Search */}
      <Box sx={{ mb: 3 }}>
        <Tabs
          value={selectedCategoryFilter}
          onChange={handleCategoryFilterChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              minHeight: 36,
              height: 36,
              px: 3,
              py: 1,
              minWidth: 'auto',
              borderRadius: '20px',
              border: '1px solid',
              borderColor: 'divider',
              bgcolor: 'rgba(24, 0, 72, 0.08)',
              color: 'text.secondary',
              transition: 'all 0.2s ease',
              '&.Mui-selected': {
                color: 'inherit',
                bgcolor: 'rgba(163, 139, 233, 0.33)',
                borderColor: 'primary.main',
                fontWeight: 600,
              },
              '&:hover': {
                bgcolor: 'action.hover',
              },
            },
            '& .MuiTabs-indicator': {
              display: 'none',
            },
            '& .MuiTabs-flexContainer': {
              gap: 1,
            },
          }}
        >
          {CATEGORY_FILTERS.map((filter, index) => (
            <Tab key={index} label={filter} />
          ))}
        </Tabs>
      </Box>

      {/* Agents Grid */}
      <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
        {filteredAgents.map((agent) => (
          <Grid item xs={12} sm={6} lg={4} key={agent.id}>
            <AgentCard
              agent={agent}
              id={agent.id}
              onOpenMenu={handleClick}
              handleId={handleIdAgent}
            />
          </Grid>
        ))}
      </Grid>

      {/* Infinite Scroll Loading */}
      {isLoading && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            py: 4,
            mt: 2,
          }}
        >
          <CircularProgress size={50} />
          <Typography variant="body2" sx={{ ml: 2, color: 'text.secondary' }}>
            Loading more templates...
          </Typography>
        </Box>
      )}

      <Menu id="simple-menu" anchorEl={anchorEl} open={openMenu} onClose={handleClose}>
        <Box sx={{ p: 1 }}>
          <MenuItem>
            <Box sx={{ display: 'flex', gap: '5px' }}>
              <Iconify icon="streamline:interface-arrows-vertical-left-right-expand-resize-bigger-horizontal-smaller-size-arrow-arrows-big" />
              <Typography> Build</Typography>
            </Box>
          </MenuItem>
          <MenuItem>
            <Box sx={{ display: 'flex', gap: '5px' }}>
              <Iconify icon="humbleicons:duplicate" />
              <Typography>Duplicate</Typography>
            </Box>
          </MenuItem>
          <Divider />
          <MenuItem onClick={hanldeDeleteAgent}>
            <Box sx={{ display: 'flex', gap: '5px' }}>
              <Iconify icon="material-symbols:delete-outline" style={{ color: '#f24848' }} />
              <Typography> Delete</Typography>
            </Box>
          </MenuItem>
        </Box>
      </Menu>

      {/* No Results */}
      {filteredAgents.length === 0 && (
        <Box
          sx={{
            textAlign: 'center',
            py: 12,
            px: 4,
          }}
        >
          <Box
            sx={{
              width: 120,
              height: 120,
              mx: 'auto',
              mb: 3,
              borderRadius: '50%',
              bgcolor: 'action.hover',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Iconify
              icon="eva:search-outline"
              width={48}
              height={48}
              sx={{ color: 'text.disabled' }}
            />
          </Box>
          <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
            No agents found
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
            Try adjusting your search or filter criteria to find the agents you&apos;re looking for.
          </Typography>
        </Box>
      )}

      <ConfirmDialog
        open={openConfirmationDelete}
        onClose={() => setOpenConfirmationDelete(false)}
        title="Delete agent"
        content="Are you sure you want to delete this agent? This action cannot be undone."
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label="Cancel"
              variant="outlined"
              onClick={() => setOpenConfirmationDelete(false)}
            />
            <AppButton
              isLoading={isPendingDeleteAgent}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
            />
          </Box>
        }
      />
    </Box>
  );
}
