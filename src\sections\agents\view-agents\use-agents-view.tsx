import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { useAgentsApi, Agent, AgentFilters } from 'src/services/api/use-agents-api';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { paths } from 'src/routes/paths';
import { useDebounce } from 'src/hooks/use-debounce';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];

export const useAgentsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openConfirmationDelete, setOpenConfirmationDelete] = useState(false);
  const [idAgent, setIdAgent] = useState<number | null>(null);
  const { useDeleteAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  const { mutate: deleteAgent, isPending: isPendingDeleteAgent } = useDeleteAgents();

  // Infinite scroll state
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);

  // Debounce search query for API calls (400ms delay)
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Filter states
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);

  // Use the categories API hook to fetch categories (same as templates)
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetCategories();

  // Create dynamic category filters with useMemo to prevent infinite re-renders (same as templates)
  const CATEGORY_FILTERS = useMemo(() => {
    if (
      !categoriesData ||
      !categoriesData.categories ||
      !Array.isArray(categoriesData.categories)
    ) {
      return ['All'];
    }
    return ['All', ...categoriesData.categories.map((category) => category.name)];
  }, [categoriesData]);

  // Build filters for API call (same logic as templates)
  const apiFilters: AgentFilters = {
    take: 10,
    skip: currentPage * 10,
    // Add search query if present (backend search)
    ...(debouncedSearchQuery && { 'template-name': debouncedSearchQuery }),
    ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
    ...(selectedCategoryFilter !== 0 && {
      categoryId: categoriesData?.categories?.[selectedCategoryFilter - 1]?.id
        ? parseInt(categoriesData.categories[selectedCategoryFilter - 1].id, 10)
        : undefined,
    }),
  };

  // Use the agents API hook to fetch data with filters
  const { useGetAgentss } = useAgentsApi();
  const { data: agentsResponse, isLoading, error, refetch } = useGetAgentss(apiFilters);

  // Extract agents from the response
  const agents = agentsResponse?.agents || [];
  const totalCount = agentsResponse?.total || 0;

  // Ensure currentPage starts from 0 when component mounts
  useEffect(() => {
    setCurrentPage(0);
    setHasNextPage(true);
    setIsLoadingMore(false);
  }, []); // Empty dependency array means this runs only once on mount

  // Update filtered agents with infinite scroll logic
  useEffect(() => {
    if (currentPage === 0) {
      // First page or reset - replace all agents
      setFilteredAgents(agents);
    } else {
      // Subsequent pages - append new agents
      setFilteredAgents((prev) => [...prev, ...agents]);
    }

    // Update pagination state based on total count and current loaded count
    setFilteredAgents((current) => {
      const currentLoadedCount = current.length;
      setHasNextPage(currentLoadedCount < totalCount && agents.length === 10);
      return current;
    });
    setIsLoadingMore(false);
  }, [agents, currentPage, totalCount]);

  // Load more agents for infinite scroll
  const loadMoreAgents = useCallback(() => {
    if (hasNextPage && !isLoadingMore && !isLoading && filteredAgents.length < totalCount) {
      console.log('Loading more agents, current page:', currentPage); // Debug log
      setIsLoadingMore(true);
      if (!isLoading) setCurrentPage((prev) => prev + 1);
    }
  }, [hasNextPage, isLoadingMore, isLoading, filteredAgents.length, totalCount, currentPage]);

  // Throttle flag to prevent rapid scroll events
  const [isScrollThrottled, setIsScrollThrottled] = useState(false);

  // Scroll detection for infinite scroll
  const handleScroll = useCallback(() => {
    if (isScrollThrottled) return; // Only throttle check needed since this is always for agents

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = window.innerHeight;

    // Load more when user is 200px from bottom
    if (scrollTop + clientHeight >= scrollHeight - 200) {
      setIsScrollThrottled(true);
      loadMoreAgents();
      // Reset throttle after 100ms
      setTimeout(() => setIsScrollThrottled(false), 100);
    }
  }, [loadMoreAgents, isScrollThrottled]);

  // Add scroll event listener
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Reset infinite scroll state when filters change
  useEffect(() => {
    setCurrentPage(0);
    setHasNextPage(true);
    setIsLoadingMore(false);
    setIsScrollThrottled(false); // Reset scroll throttle
  }, [debouncedSearchQuery, selectedTypeFilter, selectedCategoryFilter]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  const handleOpenAgent = async (obj: Agent) => {
    if (isCreating) return;

    setIsCreating(true);
    const body = {
      title: 'new chat',
      agentId: obj.id || '',
    };
    createChat(body, {
      onSuccess: (resChat) => {
        const newChatId = resChat?.data?.id;
        navigate(paths.dashboard.agents.chat(obj.template.id, obj.id, newChatId), {
          state: { name: obj.template.name },
        });
        setIsCreating(false);
      },
      onError: () => {
        setIsCreating(false);
      },
    });
  };
  // Handle filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };
  const openMenu = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const hanldeDeleteAgent = () => {
    setOpenConfirmationDelete(true);
  };

  const handleIdAgent = (id: number) => {
    setIdAgent(id);
  };

  const handleConfirmDelete = () => {
    if (idAgent) {
      deleteAgent(idAgent, {
        onSuccess: () => {
          setOpenConfirmationDelete(false);
          handleClose?.();
        },
      });
    }
  };

  return {
    // Data
    agents,
    filteredAgents,

    // Loading states
    isCreating,
    isLoading, // Only show main loading for first page
    isInitialLoading: isLoading && currentPage === 0, // Separate initial loading state
    categoriesLoading,
    error,
    refetch,

    // Infinite scroll states
    isLoadingMore,
    hasNextPage,
    loadMoreAgents,
    totalCount,

    // Filter state
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleOpenAgent,
    handleTypeFilterChange,
    handleCategoryFilterChange,
    openMenu,
    handleClick,
    handleClose,
    anchorEl,
    hanldeDeleteAgent,
    openConfirmationDelete,
    setOpenConfirmationDelete,
    handleIdAgent,
    handleConfirmDelete,
    isPendingDeleteAgent,
  };
};

export type { Agent };
